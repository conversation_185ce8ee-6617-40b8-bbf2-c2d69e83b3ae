using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ICompanyExerciseDal : IEntityRepository<CompanyExercise>
    {
        List<CompanyExerciseDto> GetCompanyExercises(int companyId);
        List<CompanyExerciseDto> GetCompanyExercisesByCategory(int companyId, int categoryId);
        PaginatedResult<CompanyExerciseDto> GetCompanyExercisesFiltered(int companyId, CompanyExerciseFilterDto filter);
        List<CompanyExerciseDto> SearchCompanyExercises(int companyId, string searchTerm);
        CompanyExerciseDto GetCompanyExerciseDetail(int companyId, int exerciseId);
        CompanyExerciseDto GetCompanyExerciseById(int companyId, int exerciseId);
        List<CombinedExerciseDto> GetCombinedExercises(int companyId); // System + Company egzersizleri birleşik
        List<CombinedExerciseDto> GetCombinedExercisesByCategory(int companyId, int categoryId);
        PaginatedResult<CombinedExerciseDto> GetCombinedExercisesFiltered(int companyId, SystemExerciseFilterDto filter);
    }
}
