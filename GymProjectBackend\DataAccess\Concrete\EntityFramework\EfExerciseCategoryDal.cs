using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExerciseCategoryDal : EfEntityRepositoryBase<ExerciseCategory, GymContext>, IExerciseCategoryDal
    {
        // Constructor injection (Scalability için)
        public EfExerciseCategoryDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfExerciseCategoryDal() : base()
        {
        }

        public List<ExerciseCategoryDto> GetAllCategories()
        {
            // DI kullanılıyor - Scalability optimized
            var result = from ec in _context.ExerciseCategories
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return result.ToList();
        }

        public List<ExerciseCategoryDto> GetActiveCategories()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from ec in _context.ExerciseCategories
                             where ec.IsActive == true
                             orderby ec.CategoryName
                             select new ExerciseCategoryDto
                             {
                                 ExerciseCategoryID = ec.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 Description = ec.Description,
                                 IsActive = ec.IsActive,
                                 CreationDate = ec.CreationDate
                             };
                return result.ToList();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from ec in context.ExerciseCategories
                                 where ec.IsActive == true
                                 orderby ec.CategoryName
                                 select new ExerciseCategoryDto
                                 {
                                     ExerciseCategoryID = ec.ExerciseCategoryID,
                                     CategoryName = ec.CategoryName,
                                     Description = ec.Description,
                                     IsActive = ec.IsActive,
                                     CreationDate = ec.CreationDate
                                 };
                    return result.ToList();
                }
            }
        }

        public ExerciseCategoryDto GetCategoryById(int categoryId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from ec in _context.ExerciseCategories
                             where ec.ExerciseCategoryID == categoryId
                             select new ExerciseCategoryDto
                             {
                                 ExerciseCategoryID = ec.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 Description = ec.Description,
                                 IsActive = ec.IsActive,
                                 CreationDate = ec.CreationDate
                             };
                return result.FirstOrDefault();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from ec in context.ExerciseCategories
                                 where ec.ExerciseCategoryID == categoryId
                                 select new ExerciseCategoryDto
                                 {
                                     ExerciseCategoryID = ec.ExerciseCategoryID,
                                     CategoryName = ec.CategoryName,
                                     Description = ec.Description,
                                     IsActive = ec.IsActive,
                                     CreationDate = ec.CreationDate
                                 };
                    return result.FirstOrDefault();
                }
            }
        }

        public IResult AddCategory(ExerciseCategoryAddDto categoryAddDto)
        {
            try
            {
                var category = new ExerciseCategory
                {
                    CategoryName = categoryAddDto.CategoryName,
                    Description = categoryAddDto.Description,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    _context.ExerciseCategories.Add(category);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (GymContext context = new GymContext())
                    {
                        context.ExerciseCategories.Add(category);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Egzersiz kategorisi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateCategory(ExerciseCategoryUpdateDto categoryUpdateDto)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var existingCategory = _context.ExerciseCategories.FirstOrDefault(c => c.ExerciseCategoryID == categoryUpdateDto.ExerciseCategoryID);
                    if (existingCategory == null)
                    {
                        return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                    }

                    existingCategory.CategoryName = categoryUpdateDto.CategoryName;
                    existingCategory.Description = categoryUpdateDto.Description;
                    existingCategory.IsActive = categoryUpdateDto.IsActive;
                    existingCategory.UpdatedDate = DateTime.Now;

                    _context.ExerciseCategories.Update(existingCategory);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (GymContext context = new GymContext())
                    {
                        var existingCategory = context.ExerciseCategories.FirstOrDefault(c => c.ExerciseCategoryID == categoryUpdateDto.ExerciseCategoryID);
                        if (existingCategory == null)
                        {
                            return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                        }

                        existingCategory.CategoryName = categoryUpdateDto.CategoryName;
                        existingCategory.Description = categoryUpdateDto.Description;
                        existingCategory.IsActive = categoryUpdateDto.IsActive;
                        existingCategory.UpdatedDate = DateTime.Now;

                        context.ExerciseCategories.Update(existingCategory);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Egzersiz kategorisi başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult DeleteCategory(int categoryId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var category = _context.ExerciseCategories.FirstOrDefault(c => c.ExerciseCategoryID == categoryId);
                    if (category == null)
                    {
                        return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                    }

                    // Soft delete
                    category.IsActive = false;
                    category.DeletedDate = DateTime.Now;

                    _context.ExerciseCategories.Update(category);
                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (GymContext context = new GymContext())
                    {
                        var category = context.ExerciseCategories.FirstOrDefault(c => c.ExerciseCategoryID == categoryId);
                        if (category == null)
                        {
                            return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                        }

                        // Soft delete
                        category.IsActive = false;
                        category.DeletedDate = DateTime.Now;

                        context.ExerciseCategories.Update(category);
                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Egzersiz kategorisi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi silinirken hata oluştu: {ex.Message}");
            }
        }
    }
}
