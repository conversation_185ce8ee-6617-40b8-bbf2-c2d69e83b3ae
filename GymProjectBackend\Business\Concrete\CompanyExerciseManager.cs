using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyExerciseManager : ICompanyExerciseService
    {
        ICompanyExerciseDal _companyExerciseDal;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public CompanyExerciseManager(ICompanyExerciseDal companyExerciseDal, 
                                     Core.Utilities.Security.CompanyContext.ICompanyContext companyContext)
        {
            _companyExerciseDal = companyExerciseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyExerciseDto>> GetCompanyExercises()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExercises(companyId);
            return new SuccessDataResult<List<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyExerciseDto>> GetCompanyExercisesByCategory(int categoryId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExercisesByCategory(companyId, categoryId);
            return new SuccessDataResult<List<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<CompanyExerciseDto>> GetCompanyExercisesFiltered(CompanyExerciseFilterDto filter)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExercisesFiltered(companyId, filter);
            return new SuccessDataResult<PaginatedResult<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<CompanyExerciseDto>> SearchCompanyExercises(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new ErrorDataResult<List<CompanyExerciseDto>>("Arama terimi boş olamaz.");
            }

            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.SearchCompanyExercises(companyId, searchTerm);
            return new SuccessDataResult<List<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<CompanyExerciseDto> GetCompanyExerciseDetail(int exerciseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExerciseDetail(companyId, exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<CompanyExerciseDto>("Salon egzersizi bulunamadı.");
            }

            return new SuccessDataResult<CompanyExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<CompanyExerciseDto> GetById(int exerciseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExerciseById(companyId, exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<CompanyExerciseDto>("Salon egzersizi bulunamadı.");
            }

            return new SuccessDataResult<CompanyExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Add(CompanyExerciseAddDto exerciseAddDto)
        {
            var companyId = _companyContext.GetCompanyId();
            
            var exercise = new CompanyExercise
            {
                CompanyID = companyId,
                ExerciseCategoryID = exerciseAddDto.ExerciseCategoryID,
                ExerciseName = exerciseAddDto.ExerciseName,
                Description = exerciseAddDto.Description,
                Instructions = exerciseAddDto.Instructions,
                MuscleGroups = exerciseAddDto.MuscleGroups,
                Equipment = exerciseAddDto.Equipment,
                DifficultyLevel = exerciseAddDto.DifficultyLevel,
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _companyExerciseDal.Add(exercise);
            return new SuccessResult("Salon egzersizi başarıyla eklendi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Update(CompanyExerciseUpdateDto exerciseUpdateDto)
        {
            var companyId = _companyContext.GetCompanyId();
            var existingExercise = _companyExerciseDal.Get(e => e.CompanyExerciseID == exerciseUpdateDto.CompanyExerciseID && 
                                                              e.CompanyID == companyId);
            if (existingExercise == null)
            {
                return new ErrorResult("Salon egzersizi bulunamadı.");
            }

            existingExercise.ExerciseCategoryID = exerciseUpdateDto.ExerciseCategoryID;
            existingExercise.ExerciseName = exerciseUpdateDto.ExerciseName;
            existingExercise.Description = exerciseUpdateDto.Description;
            existingExercise.Instructions = exerciseUpdateDto.Instructions;
            existingExercise.MuscleGroups = exerciseUpdateDto.MuscleGroups;
            existingExercise.Equipment = exerciseUpdateDto.Equipment;
            existingExercise.DifficultyLevel = exerciseUpdateDto.DifficultyLevel;
            existingExercise.IsActive = exerciseUpdateDto.IsActive;
            existingExercise.UpdatedDate = DateTime.Now;

            _companyExerciseDal.Update(existingExercise);
            return new SuccessResult("Salon egzersizi başarıyla güncellendi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int exerciseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var exercise = _companyExerciseDal.Get(e => e.CompanyExerciseID == exerciseId && e.CompanyID == companyId);
            if (exercise == null)
            {
                return new ErrorResult("Salon egzersizi bulunamadı.");
            }

            // Soft delete
            exercise.IsActive = false;
            exercise.DeletedDate = DateTime.Now;

            _companyExerciseDal.Update(exercise);
            return new SuccessResult("Salon egzersizi başarıyla silindi.");
        }

        // Birleşik egzersiz listesi (System + Company)
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<CombinedExerciseDto>> GetCombinedExercises()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCombinedExercises(companyId);
            return new SuccessDataResult<List<CombinedExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<CombinedExerciseDto>> GetCombinedExercisesByCategory(int categoryId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCombinedExercisesByCategory(companyId, categoryId);
            return new SuccessDataResult<List<CombinedExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<CombinedExerciseDto>> GetCombinedExercisesFiltered(SystemExerciseFilterDto filter)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCombinedExercisesFiltered(companyId, filter);
            return new SuccessDataResult<PaginatedResult<CombinedExerciseDto>>(result);
        }
    }
}
